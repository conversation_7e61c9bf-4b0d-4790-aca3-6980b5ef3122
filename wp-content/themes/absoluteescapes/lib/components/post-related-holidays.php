<?php

/**
 * Post Related Holidays
 */

// Get related holidays configuration
$related_holidays_config = get_field('related_holidays');
$holidays = [];
$show_related_holidays = false;

if ($related_holidays_config && $related_holidays_config['enabled']) {
    // Check if holidays are selected
    if (!empty($related_holidays_config['selected_holidays'])) {
        // Use selected holidays
        foreach ($related_holidays_config['selected_holidays'] as $selected_holiday) {
            if ($selected_holiday['holiday']) {
                $holidays[] = $selected_holiday['holiday'];
            }
        }
        $show_related_holidays = !empty($holidays);
    }
}

?>

<?php if($show_related_holidays && $holidays && !empty($holidays)) : ?>
<section class="related-posts">
    <div class="related-posts__inner" data-aos="fade">
        <div class="related-posts__container">
            <div class="related-posts__content">
                <h2 class="related-posts__heading h2-large heading-light heading-underlined text-weight-regular centre"><?php _e('Related holidays', 'absoluteescapes'); ?></h2>
                <div class="holidays__posts">
                    <?php foreach($holidays as $holiday) : ?>
                        <?php

                        $price = get_field('holiday_price', $holiday);
                        $price_suffix_short = get_field('holiday_price_suffix_short', $holiday);
                        $distance = get_field('holiday_distance', $holiday);
                        $min_duration = get_field('holiday_minimum_duration', $holiday);
                        $max_duration = get_field('holiday_maximum_duration', $holiday);
                        if($max_duration == 999) {
                            $max_duration = '';
                        }
                        $types = get_the_terms($holiday, 'holiday-type');

                        ?>

                        <div class="holidays__post">

                                <div class="row holidays__post-row">
                                    <div class="col-12 holidays__post-col">
                                        <?php if(have_rows('holiday_gallery', $holiday)) : ?>
                                            <div class="holidays__gallery">
                                                <?php while(have_rows('holiday_gallery', $holiday)) : the_row(); ?>
                                                    <?php

                                                    $image = wp_get_attachment_image(get_sub_field('image'), 'carousel');

                                                    ?>

                                                    <?php if($image) : ?>
                                                        <div class="holidays__image">
                                                            <?php echo $image; ?>
                                                        </div>
                                                    <?php endif; ?>

                                                <?php endwhile; ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="col-12 holidays__post-col">
                                        <a href="<?php echo get_the_permalink($holiday); ?>">
                                        <div class="holidays__post-content">
                                            <h4 class="holidays__title"><?php echo get_the_title($holiday); ?></h4>
                                            <?php if($price) : ?>
                                                <h4 class="holidays__price h4-small text-teal text-weight-semibold">
                                                    <span><?php _e('From', 'absoluteescapes'); ?> <?php echo $price; ?> <?php echo $price_suffix_short; ?></span>
                                                </h4>
                                            <?php endif; ?>

                                            <?php

                                            $mtypes = get_the_terms($holiday->ID, 'holiday-type');
                                            $mDays = get_field('holiday_minimum_duration', $holiday->ID);
                                            $mxDays = get_field('holiday_maximum_duration', $holiday->ID);
                                            if($mxDays == 999) {
                                                $mxDays = '';
                                            }

                                            if($mDays === $mxDays) {
                                                $mxDays = '';
                                            }

                                            $nights_days = get_field('holiday_day_night', $holiday->ID);
                                            $mDistance = get_field('holiday_distance', $holiday->ID);

                                            ?>

                                            <div class="marker__items">
                                                <?php if($mtypes) : ?>
                                                    <div class="marker__item">
                                                        <?php foreach($mtypes as $type) : ?>
                                                            <?php

                                                            $icon = get_field('holiday_type_icon', $type);
                                                            $icon_fa = get_field('holiday_type_icon_fa', $type);
                                                            $colour = get_field('holiday_type_colour', $type);


                                                            if($type->parent) {
                                                                continue;
                                                            }

                                                            ?>

                                                            <div class="holidays__type cat">
                                                                <?php if($icon) : ?>
                                                                    <span class="holidays__icon cat__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><img src="<?php echo $icon['url']; ?>" alt="<?php echo $icon['alt']; ?>"></span>
                                                                <?php else : ?>
                                                                    <?php if($icon_fa) : ?>
                                                                        <span class="holidays__icon cat__icon" style="background-color: <?php echo ($colour) ? $colour : '#3e5056'; ?>"><i class="<?php echo $icon_fa; ?>"></i></span>
                                                                    <?php endif; ?>
                                                                <?php endif; ?>
                                                                <span class="holidays__type-text cat__text"><?php echo $type->name; ?></span>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if($mDays) : ?>
                                                    <div class="marker__item">
                                                        <div class="cat">
                                                            <span class="cat__icon" style="background-color: #3e5056;"><i class="fal fa-calendar-alt"></i></span>
                                                            <span class="cat__text"><?php echo $mDays ?><?php if($mxDays) : ?><?php echo ' - ' . $mxDays; ?><?php endif; ?><?php echo ' ' . $nights_days; ?></span>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                                <?php if($mDistance) : ?>
                                                    <div class="marker__item">
                                                        <div class="cat">
                                                            <span class="cat__icon" style="background-color: #3e5056;"><i class="fal fa-wave-sine"></i></span>
                                                            <span class="cat__text"><?php echo $mDistance; ?></span>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>
                                            </div>


                                        </div>
                                        </a>
                                    </div>
                                </div>

                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</section><!-- .related-posts -->
<?php endif; ?>
