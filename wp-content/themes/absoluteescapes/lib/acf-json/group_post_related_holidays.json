{"key": "group_post_related_holidays", "title": "Post - Related Holidays", "fields": [{"key": "field_post_related_holidays_group", "label": "Related Holidays", "name": "related_holidays", "aria-label": "", "type": "group", "instructions": "Manage the 'Related holidays' section that appears at the bottom of blog posts. If disabled or no holidays are selected, the section will not be displayed.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "layout": "block", "sub_fields": [{"key": "field_post_related_holidays_enabled", "label": "Show Related Holidays", "name": "enabled", "aria-label": "", "type": "true_false", "instructions": "Enable to show the Related Holidays section at the bottom of this blog post.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "50", "class": "", "id": ""}, "message": "", "default_value": 0, "ui_on_text": "Show", "ui_off_text": "<PERSON>de", "ui": 1}, {"key": "field_post_related_holidays_selected", "label": "Selected Holidays", "name": "selected_holidays", "aria-label": "", "type": "repeater", "instructions": "Select up to 4 holidays to display in the related holidays section. They will be displayed in a 2-column grid on desktop and 1-column on mobile.", "required": 0, "conditional_logic": [[{"field": "field_post_related_holidays_enabled", "operator": "==", "value": "1"}]], "wrapper": {"width": "", "class": "", "id": ""}, "min": 0, "max": 4, "layout": "table", "button_label": "Add Holiday", "collapsed": "", "rows_per_page": 20, "sub_fields": [{"key": "field_post_related_holidays_holiday", "label": "Holiday", "name": "holiday", "aria-label": "", "type": "post_object", "instructions": "", "required": 1, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["holiday"], "taxonomy": "", "return_format": "object", "multiple": 0, "allow_null": 0, "ui": 1, "bidirectional_target": []}]}]}], "location": [[{"param": "post_type", "operator": "==", "value": "post"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "", "show_in_rest": 0, "modified": 1734537600}